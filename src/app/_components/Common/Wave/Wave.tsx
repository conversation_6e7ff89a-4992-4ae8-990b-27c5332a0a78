import { cn } from '@/src/app/_utils';

interface WaveProps {
  className?: string;
  gradientFrom?: string;
  gradientTo?: string;
  animate?: boolean;
}

export function Wave({
  className,
  gradientFrom = 'black',
  gradientTo = '#F1F5F9',
  animate = false,
}: WaveProps) {
  const gradientId = `wave-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={cn('z-0 mt-16 w-full', className)}>
      <svg
        className={cn(
          'z-0 h-auto w-full',
          animate && 'transition-transform duration-1000 ease-in-out hover:scale-105'
        )}
        width="1024"
        height="583"
        viewBox="0 0 1024 583"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
        role="img"
        aria-label="Decorative wave"
      >
        <path
          d="M1024 582.928L0.000160775 582.928C0.00033553 222.839 0.000160775 5.92815 0.000160775 5.92815C585.041 -30.1621 382.813 114.839 1023 5.92867L1024 582.928Z"
          fill={`url(#${gradientId})`}
        />
        <defs>
          <linearGradient
            id={gradientId}
            x1="551.232"
            y1="540"
            x2="607.803"
            y2="45.307"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor={gradientFrom} />
            <stop offset="1" stopColor={gradientTo} />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
}

export default Wave;
