import { Wave } from '@/src/app/_components/Common/Wave/Wave';
import { render, screen } from '@testing-library/react';

describe('Wave', () => {
  it('renders the wave SVG with default props', () => {
    render(<Wave />);

    const wave = screen.getByRole('img', { name: 'Decorative wave' });
    expect(wave).toBeInTheDocument();
    expect(wave).toHaveAttribute('viewBox', '0 0 1024 583');
    expect(wave).toHaveAttribute('preserveAspectRatio', 'none');
  });

  it('applies custom className', () => {
    const { container } = render(<Wave className="custom-class" />);

    const waveContainer = container.firstChild;
    expect(waveContainer).toHaveClass('custom-class');
  });

  it('applies animation classes when animate is true', () => {
    render(<Wave animate={true} />);

    const wave = screen.getByRole('img', { name: 'Decorative wave' });
    expect(wave).toHaveClass(
      'transition-transform',
      'duration-1000',
      'ease-in-out',
      'hover:scale-105'
    );
  });

  it('does not apply animation classes when animate is false', () => {
    render(<Wave animate={false} />);

    const wave = screen.getByRole('img', { name: 'Decorative wave' });
    expect(wave).not.toHaveClass('transition-transform');
  });

  it('uses custom gradient colors when provided', () => {
    render(<Wave gradientFrom="#FF0000" gradientTo="#00FF00" />);

    const stops = screen.getAllByText('', { selector: 'stop' });
    expect(stops[0]).toHaveAttribute('stop-color', '#FF0000');
    expect(stops[1]).toHaveAttribute('stop-color', '#00FF00');
  });

  it('has proper responsive classes', () => {
    const { container } = render(<Wave />);

    const waveContainer = container.firstChild;
    expect(waveContainer).toHaveClass('mb-[-58%]', 'md:mb-[-50%]');
  });

  it('maintains proper z-index layering', () => {
    const { container } = render(<Wave />);

    const waveContainer = container.firstChild;
    const wave = screen.getByRole('img', { name: 'Decorative wave' });

    expect(waveContainer).toHaveClass('z-0');
    expect(wave).toHaveClass('z-0');
  });

  it('generates unique gradient IDs for multiple instances', () => {
    const { container: container1 } = render(<Wave />);
    const { container: container2 } = render(<Wave />);

    const gradient1 = container1.querySelector('linearGradient');
    const gradient2 = container2.querySelector('linearGradient');

    expect(gradient1?.id).toBeDefined();
    expect(gradient2?.id).toBeDefined();
    expect(gradient1?.id).not.toBe(gradient2?.id);
  });
});
